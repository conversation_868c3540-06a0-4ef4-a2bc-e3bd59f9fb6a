{"success": true, "data": {"content": "\n\n[![Mendable logo](https://mendable.ai/Frame%20566%20(2)Mendable](/)\n\n*   Getting started\n*   Use Cases\n*   [Docs](https://docs.mendable.ai)\n    \n*   [Pricing](/pricing)\n    \n*   [Blog](/blog)\n    \n\nOpen main menu\n\n[Sign In](/signin)\n[Get Started](/signup)\n\n![](https://mendable.ai/fullbgdsm.png)\n\n[$ npm i @mendable/search](https://docs.mendable.ai)\n\nJust in time answers  \nfor Sales and Support\n============================================\n\nTrain a secure AI on your technical resources that answers customer and employee questions so your team doesn't have to\n\nGet Started\n\nTalk to Us\n\nBacked BY\n\n![Y Combinator Logo](https://mendable.ai/yc.svg)Combinator\n\ninvisible\n\nAssistant\n\nHi, how can I help you?\n\nGenerating\n\nLoading...\n\n![Mendable loading placeholder image](https://mendable.ai/heroloading.png)\n\nFrom small startups to Fortune 500\n\nTrusted by top companies\n------------------------\n\n![Snapchat](https://mendable.ai/customers/snapchat2.svg)\n\n![MongoDB](https://mendable.ai/customers/mongo.svg)\n\n![Langchain](https://mendable.ai/customers/coinbase.svg)\n\n![Worldline](https://mendable.ai/customers/world.svg)\n\n![Nylas](https://mendable.ai/customers/nylass.svg)\n\n![Spectrocloud](https://mendable.ai/customers/spectro.svg)\n\n![Merge](https://mendable.ai/customers/merge.svg)\n\n![0x](https://mendable.ai/customers/zeroxx.svg)\n\n![Tecton.ai](https://mendable.ai/customers/tecton.svg)\n\n![Llama Index](https://mendable.ai/customers/llamaindex.png)\n\nDeploy a knowledgable technical AI anywhere\n\nUse Mendable for\n----------------\n\n[Docs & Knowledge Base](/usecases/documentation)\n\n-------------------------------------------------\n\nDecrease tickets & activation times with an AI assistant\n\n[Customer Success Enablement](/usecases/cs-enablement)\n\n-------------------------------------------------------\n\nUse a technical AI copilot to increase retention\n\n[Sales Enablement](/usecases/sales-enablement)\n\n-----------------------------------------------\n\nUse a technical AI copilot to build trust with prospects\n\n[Product Copilot](/usecases/productcopilot)\n\n--------------------------------------------\n\nSpeed up adoption with a technical assistant in your app\n\nSee how companies implement Mendable\n------------------------------------\n\n![](https://mendable.ai/langchain.png)\n\n[Langchain Docs](https://python.langchain.com)\n\n-----------------------------------------------\n\nOne of the most popular frameworks for developing AI applications\n\nhttps://python.langchain.com\n\n![](https://mendable.ai/0xlogo.png)\n\n[0x Docs](https://0x.org/docs)\n\n-------------------------------\n\n0x offers the core building blocks to create the most powerful Web3 apps\n\nhttps://0x.org/docs\n\n![](https://mendable.ai/zenlytics.png)\n\n[Zenlytics](https://docs.zenlytic.com)\n\n---------------------------------------\n\nSelf-serve analytics tool that helps you answer the deeper questions you have about your data\n\nhttps://docs.zenlytic.com\n\n![](https://mendable.ai/LlamaIndex.png)\n\n[Llama Index](http://gpt-index.readthedocs.io)\n\n-----------------------------------------------\n\nA central interface to connect your LLM’s with external data.\n\nhttp://gpt-index.readthedocs.io\n\n![](https://mendable.ai/spectrocloud-white.png)\n\n[Spectrocloud](https://docs.spectrocloud.com/)\n\n-----------------------------------------------\n\nK8s management uniquely built for scale. Manage the lifecycle of any type of cluster.\n\nhttps://docs.spectrocloud.com/\n\n![](https://mendable.ai/codegpt.png)\n\n[Code GPT](https://www.codegpt.co/)\n\n------------------------------------\n\nWith over 450,000 installs, CodeGPT brings AI inside your code editor.\n\nhttps://www.codegpt.co/\n\nAnd many more...\n\nFrom SSO to BYOK\n\nEnterprise-grade security\n-------------------------\n\n#### SOC 2 Type II\n\nMendable is SOC 2 Type II certified. Check out our [AI Trust Center](https://mendable.wolfia.com/?ref=mendable-website)\n for additional information.\n\n#### SSO (SAML, OIDC, OAuth)\n\nSupports SAML 2.0, OpenID Connect, and OAuth 2.0 for single sign-on (SSO) and identity federation.\n\n#### RBAC (Project and chunk level)\n\nRole-based access control to ensure that only the right people have access to the right data.\n\n#### Secure Data Connectors\n\nIntegrate securely to Google Drive, Salesforce, Zendesk and more using OAuth 2.0.\n\n#### BYOK / BYOM\n\nBring your own key or custom model to Mendable to ensure compliance.\n\n#### Rate Limiting\n\nProject and user rate limit protection to prevent abuse and ensure availability.\n\nOver 20+ data connectors\n\nStart by connecting your data\n-----------------------------\n\nMendable offers managed ingestion through a simple online GUI and through our API. You can easily add, modify, or delete different types of sources.\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![github-mark](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![medium](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![gitbook](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![docusaurus](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![readme](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![notion](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![upload](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![confluence](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![drive](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![youtube](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![pdf](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![wordpress](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![slack](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![readme](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![website](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![zendesk](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\nEasily Teach Your Model\n\nCustomize your model\n--------------------\n\nCustomize base model properties\n\nGPT-3.5-Turbo and GPT-4 are supported with a variety of base models coming soon\n\nTraining through answer correction\n\nCorrect the answers generated by the model and it will instantly learn from your feedback\n\nCustom prompt edits\n\nEdit the prompt to prevent hallucinations, maintain voice and format requirements\n\nKeep your data always updated\n\nMendable reingestion process offers CRON jobs and webhooks to keep your data synced and always up to date\n\nSupport Link\n\nHave customers redirected to your customer support link when the bot can't answer their questions\n\nPrivacy-first features\n\nMendable provides custom private, open source LLMs depending on your needs\n\n### Make it perfect for your use case\n\nWe know every uses case is slightly different so the Mendable platform allows you to customize your model to fit your company's needs through multiple features.\n\n*   Support for multiple base LLM models (including privacy first models)\n*   Training through answer correction\n*   Custom prompt edits\n*   Model creativity control\n\nTeach Model\n\nContinuous Training\n-------------------\n\nCoach the model by correcting the wrong responses, keeping your chat applications always up to date\n\nMore than just a chatbot\n\nTools and Actions\n-----------------\n\nGive your AI access to tools for augmentation and actions for automation. Integrate with any API\n\n![](https://mendable.ai/tools-purple.svg)![](https://mendable.ai/actions-yellow-svg.svg)\n\n![](https://mendable.ai/tools-pic.png)\n\nReact, Vanilla JS, API\n\nChoose your component\n---------------------\n\nMendable provides a variety of components ranging from search bars, to chat bubbles, to full CLIs built on our API. Customize them or easily build your own\n\n![Mendable component](https://mendable.ai/Frame%20597%20(3)\n\n    \n    import { MendableSearchBar } from '@mendable/search'\n    <MendableSearchBar anon_key={MENDABLE_ANON_KEY} />\n    \n\nFrom zero to production in minutes\n\nDeploy anywhere\n---------------\n\nDeploy Mendable internally, externally, or both. Our API allows you to send and query data from anywhere.\n\nView Documentation\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2756%27%20height=%2756%27/%3e)![Saif Khan](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\nMendables integration on Nylas is a goldmine of data. Now, the product team has a direct source of user feedback, questions, and problems. It's amazing!\n\nSaif Khan \\- Product @ NylasKarl Cardenas \\- Director @ SpectroCloudGuillermo Rauch \\- CEO @ Vercel\n\nAI Chat Infrastructure built for production\n\nEnterprise ready out of the box\n-------------------------------\n\nVerified Sources\n----------------\n\nReduce hallucinations by grounding answers with sources from your documentation\n\nEnterprise Grade Security\n-------------------------\n\nOur platform is built for enterprises in mind. We provide RBAC, bring your own model, and SLAs\n\nReady for the whole team\n------------------------\n\nMendable supports single-sign-on so your entire team can train, manage your custom AI\n\nExplore your dashboard\n\nGet insights from usage\n-----------------------\n\nUsage\n\nNumber of chat messages per month\n\n### Understand all interactions\n\nUnravel your users' queries, track their interactions, customize responses, and monitor your product usage effortlessly.\n\n*   \\-Gain key insights into user queries\n*   \\-Monitor real-time product-user interactions\n*   \\-Fine-tune your model for optimized responses\n*   \\-Track and evaluate Mendable usage\n\n### Insights beyond conversations\n\nLearn what your users are asking, how they are asking, and their satisfaction level with the answers. Teach the model based on the answers rating and improve the model's performance.\n\nOur wall of love\n\nDon't take our word for it\n--------------------------\n\nEmpower your users with AI powered search\n\nBuild an AI technical assistant in minutes\n------------------------------------------\n\nTry it out\n\nFrequently asked questions\n--------------------------\n\nIf you have anything else you want to ask,[reach out to us](mailto:<EMAIL>)\n.\n\n*   *   ### Is it free?\n        \n        We have a free plan that gives you 500 message credits. It is also free for certain Open source projects. Contact us to see if your project is eligible.\n        \n    *   ### Do you train your AI model with my code?\n        \n        Currently, Mendable does not look at any of your repository's code. However, in the future we may add it. We will always give you the option to opt out of sharing your data.\n        \n*   *   ### How do I remove the Powered by Mendable?\n        \n        To remove the Powered by Mendable, you need to upgrade to an enterprise or custom plan. Contact us at [<EMAIL>](mailto:<EMAIL>)\n         and we can help you out.\n        \n    *   ### How do I get an anon key?\n        \n        To get your anon key you need to sign up at [mendable.ai](https://mendable.ai)\n         and create a project. Then you can find your anon key in the API Keys section of the dashboard. Anon keys are used for client-side while API keys are used for server-side.\n        \n*   *   ### Which model does Mendable use?\n        \n        Mendable offers gpt-3.5-turbo, gpt-4, claude-2 and more. If you'd like a custom model, contact us and we can help you out.\n        \n    *   ### Is GPT-4 pricing different?\n        \n        Yes, right now GPT-4 will cost 3 requests per message instead of 1 (gpt-3.5-turbo). That means that instead of 500 messages, you will get around 166 messages if you only use GPT-4.\n        \n*   *   ### Can you correct the AI response?\n        \n        Yes, Mendable offers a 'teach the model' functionality where you can correct the AI response and it will learn from it.\n        \n    *   ### How can I integrate Mendable with my application?\n        \n        Probably! Check out the Mendable documentation here [https://docs.mendable.ai](https://docs.mendable.ai)\n         to better understand how you can start integrating.\n        \n*   *   ### Is it 100% accurate?\n        \n        Like Humans, AI will never be 100% accurate. So we can't assure you that every solution will be correct.\n        \n    *   ### How do I cancel my subscription?\n        \n        Simply log into our platform, go to your account and click on \"Open customer portal\" button. There you will be able to cancel/modify it through Stripe.\n        \n*   *   ### How does Mendable work?\n        \n        Our application syncs with your documentation and support channels, then uses your docs and previously answered questions to suggest possible answers.\n        \n    *   ### Are you open-source?\n        \n        Currently not - although we have some open source components and integrations. If you have input here, please message us at.[<EMAIL>](mailto:<EMAIL>)\n        .\n        \n*   *   ### How does Mendable price custom plans?\n        \n        #### 1\\. Use case\n        \n        *   Mendable differentiates between internal and external use cases.\n        *   With Mendable, we give you the ability to use our chat bots for a variety of use cases, both for internal efficiency and external communication to your customers.\n        \n        #### 2\\. Total usage\n        \n        *   For specifically external use cases, you will only pay for the value you're receiving.\n        *   Mendable will look at the total number of messages sent during a month.\n        \n        #### 3\\. Custom work\n        \n        *   If there are any special feature requests (custom data connectors, etc.), we are happy to discuss these requirements!\n        \n\nWe use tracking cookies to understand how you use the product and help us improve it!  \nPlease accept cookies to help us improve.\n\nAccept CookiesDecline Cookies\n\n![Mendable logo](https://mendable.ai/Frame%20566%20(2)[Mendable](#_)\n\n[Instagram](https://instagram.com/sideguide.dev)\n[Twitter](https://twitter.com/mendableai)\n[GitHub](https://github.com/sideguide)\n[Discord](https://discord.com/invite/kJufGDb7AA)\n\n![SOC 2 Type II](https://mendable.ai/soc2type2badge.png)\n\nDocumentation\n\n*   [Getting Started](/signup)\n    \n\n*   [API Docs](https://docs.mendable.ai)\n    \n\n*   [Integrations](https://docs.mendable.ai/integrations/slack)\n    \n\n*   [Examples](https://docs.mendable.ai/examples)\n    \n\n*   [Tools & Actions](https://docs.mendable.ai/tools)\n    \n\nUse Cases\n\n*   [Sales Enablement](/usecases/sales-enablement)\n    \n\n*   [Knowledge Base](/usecases/documentation)\n    \n\n*   [CS Enablement](/usecases/cs-enablement)\n    \n\n*   [Product Copilot](/usecases/productcopilot)\n    \n\nResources\n\n*   [Pricing](/pricing)\n    \n\n*   [Changelog](https://docs.mendable.ai/changelog)\n    \n\n*   [Security](/security)\n    \n\n*   [AI Trust Center](https://mendable.wolfia.com/?ref=mendable-footer)\n    \n\nCompany\n\n*   [Blog](/blog)\n    \n\n*   [Contact](mailto:<EMAIL>)\n    \n\n© 2024 SideGuide - SideGuide Technologies Inc.\n\n[System Status](https://mendable.betteruptime.com)\n\n[Status](https://mendable.betteruptime.com)\n[Privacy Policy](/privacy-policy)\n[Privacy](/privacy-policy)\n[Terms](/terms-of-conditions)", "markdown": "\n\n[![Mendable logo](/Frame 566 (2).png)Mendable](/)\n\n*   Getting started\n*   Use Cases\n*   [Docs](https://docs.mendable.ai)\n    \n*   [Pricing](/pricing)\n    \n*   [Blog](/blog)\n    \n\nOpen main menu\n\n[Sign In](/signin)\n[Get Started](/signup)\n\n![](/fullbgdsm.png)\n\n[$ npm i @mendable/search](https://docs.mendable.ai)\n\nJust in time answers  \nfor Sales and Support\n============================================\n\nTrain a secure AI on your technical resources that answers customer and employee questions so your team doesn't have to\n\nGet Started\n\nTalk to Us\n\nBacked BY\n\n![Y Combinator Logo](/yc.svg)Combinator\n\ninvisible\n\nAssistant\n\nHi, how can I help you?\n\nGenerating\n\nLoading...\n\n![Mendable loading placeholder image](/heroloading.png)\n\nFrom small startups to Fortune 500\n\nTrusted by top companies\n------------------------\n\n![Snapchat](/customers/snapchat2.svg)\n\n![MongoDB](/customers/mongo.svg)\n\n![Langchain](/customers/coinbase.svg)\n\n![Worldline](/customers/world.svg)\n\n![Nylas](/customers/nylass.svg)\n\n![Spectrocloud](/customers/spectro.svg)\n\n![Merge](/customers/merge.svg)\n\n![0x](/customers/zeroxx.svg)\n\n![Tecton.ai](/customers/tecton.svg)\n\n![Llama Index](/customers/llamaindex.png)\n\nDeploy a knowledgable technical AI anywhere\n\nUse Mendable for\n----------------\n\n[Docs & Knowledge Base](/usecases/documentation)\n\n-------------------------------------------------\n\nDecrease tickets & activation times with an AI assistant\n\n[Customer Success Enablement](/usecases/cs-enablement)\n\n-------------------------------------------------------\n\nUse a technical AI copilot to increase retention\n\n[Sales Enablement](/usecases/sales-enablement)\n\n-----------------------------------------------\n\nUse a technical AI copilot to build trust with prospects\n\n[Product Copilot](/usecases/productcopilot)\n\n--------------------------------------------\n\nSpeed up adoption with a technical assistant in your app\n\nSee how companies implement Mendable\n------------------------------------\n\n![](/langchain.png)\n\n[Langchain Docs](https://python.langchain.com)\n\n-----------------------------------------------\n\nOne of the most popular frameworks for developing AI applications\n\nhttps://python.langchain.com\n\n![](/0xlogo.png)\n\n[0x Docs](https://0x.org/docs)\n\n-------------------------------\n\n0x offers the core building blocks to create the most powerful Web3 apps\n\nhttps://0x.org/docs\n\n![](/zenlytics.png)\n\n[Zenlytics](https://docs.zenlytic.com)\n\n---------------------------------------\n\nSelf-serve analytics tool that helps you answer the deeper questions you have about your data\n\nhttps://docs.zenlytic.com\n\n![](/LlamaIndex.png)\n\n[Llama Index](http://gpt-index.readthedocs.io)\n\n-----------------------------------------------\n\nA central interface to connect your LLM’s with external data.\n\nhttp://gpt-index.readthedocs.io\n\n![](/spectrocloud-white.png)\n\n[Spectrocloud](https://docs.spectrocloud.com/)\n\n-----------------------------------------------\n\nK8s management uniquely built for scale. Manage the lifecycle of any type of cluster.\n\nhttps://docs.spectrocloud.com/\n\n![](/codegpt.png)\n\n[Code GPT](https://www.codegpt.co/)\n\n------------------------------------\n\nWith over 450,000 installs, CodeGPT brings AI inside your code editor.\n\nhttps://www.codegpt.co/\n\nAnd many more...\n\nFrom SSO to BYOK\n\nEnterprise-grade security\n-------------------------\n\n#### SOC 2 Type II\n\nMendable is SOC 2 Type II certified. Check out our [AI Trust Center](https://mendable.wolfia.com/?ref=mendable-website)\n for additional information.\n\n#### SSO (SAML, OIDC, OAuth)\n\nSupports SAML 2.0, OpenID Connect, and OAuth 2.0 for single sign-on (SSO) and identity federation.\n\n#### RBAC (Project and chunk level)\n\nRole-based access control to ensure that only the right people have access to the right data.\n\n#### Secure Data Connectors\n\nIntegrate securely to Google Drive, Salesforce, Zendesk and more using OAuth 2.0.\n\n#### BYOK / BYOM\n\nBring your own key or custom model to Mendable to ensure compliance.\n\n#### Rate Limiting\n\nProject and user rate limit protection to prevent abuse and ensure availability.\n\nOver 20+ data connectors\n\nStart by connecting your data\n-----------------------------\n\nMendable offers managed ingestion through a simple online GUI and through our API. You can easily add, modify, or delete different types of sources.\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![github-mark](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![medium](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![gitbook](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![docusaurus](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![readme](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![notion](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![upload](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![confluence](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![drive](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![youtube](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![pdf](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![wordpress](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![slack](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![readme](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![website](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2736%27%20height=%2736%27/%3e)![zendesk](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\nEasily Teach Your Model\n\nCustomize your model\n--------------------\n\nCustomize base model properties\n\nGPT-3.5-Turbo and GPT-4 are supported with a variety of base models coming soon\n\nTraining through answer correction\n\nCorrect the answers generated by the model and it will instantly learn from your feedback\n\nCustom prompt edits\n\nEdit the prompt to prevent hallucinations, maintain voice and format requirements\n\nKeep your data always updated\n\nMendable reingestion process offers CRON jobs and webhooks to keep your data synced and always up to date\n\nSupport Link\n\nHave customers redirected to your customer support link when the bot can't answer their questions\n\nPrivacy-first features\n\nMendable provides custom private, open source LLMs depending on your needs\n\n### Make it perfect for your use case\n\nWe know every uses case is slightly different so the Mendable platform allows you to customize your model to fit your company's needs through multiple features.\n\n*   Support for multiple base LLM models (including privacy first models)\n*   Training through answer correction\n*   Custom prompt edits\n*   Model creativity control\n\nTeach Model\n\nContinuous Training\n-------------------\n\nCoach the model by correcting the wrong responses, keeping your chat applications always up to date\n\nMore than just a chatbot\n\nTools and Actions\n-----------------\n\nGive your AI access to tools for augmentation and actions for automation. Integrate with any API\n\n![](/tools-purple.svg)![](/actions-yellow-svg.svg)\n\n![](/tools-pic.png)\n\nReact, Vanilla JS, API\n\nChoose your component\n---------------------\n\nMendable provides a variety of components ranging from search bars, to chat bubbles, to full CLIs built on our API. Customize them or easily build your own\n\n![Mendable component](/Frame 597 (3).png)\n\n    \n    import { MendableSearchBar } from '@mendable/search'\n    <MendableSearchBar anon_key={MENDABLE_ANON_KEY} />\n    \n\nFrom zero to production in minutes\n\nDeploy anywhere\n---------------\n\nDeploy Mendable internally, externally, or both. Our API allows you to send and query data from anywhere.\n\nView Documentation\n\n![](data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%2756%27%20height=%2756%27/%3e)![Saif Khan](data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)\n\nMendables integration on Nylas is a goldmine of data. Now, the product team has a direct source of user feedback, questions, and problems. It's amazing!\n\nSaif Khan \\- Product @ NylasKarl Cardenas \\- Director @ SpectroCloudGuillermo Rauch \\- CEO @ Vercel\n\nAI Chat Infrastructure built for production\n\nEnterprise ready out of the box\n-------------------------------\n\nVerified Sources\n----------------\n\nReduce hallucinations by grounding answers with sources from your documentation\n\nEnterprise Grade Security\n-------------------------\n\nOur platform is built for enterprises in mind. We provide RBAC, bring your own model, and SLAs\n\nReady for the whole team\n------------------------\n\nMendable supports single-sign-on so your entire team can train, manage your custom AI\n\nExplore your dashboard\n\nGet insights from usage\n-----------------------\n\nUsage\n\nNumber of chat messages per month\n\n### Understand all interactions\n\nUnravel your users' queries, track their interactions, customize responses, and monitor your product usage effortlessly.\n\n*   \\-Gain key insights into user queries\n*   \\-Monitor real-time product-user interactions\n*   \\-Fine-tune your model for optimized responses\n*   \\-Track and evaluate Mendable usage\n\n### Insights beyond conversations\n\nLearn what your users are asking, how they are asking, and their satisfaction level with the answers. Teach the model based on the answers rating and improve the model's performance.\n\nOur wall of love\n\nDon't take our word for it\n--------------------------\n\nEmpower your users with AI powered search\n\nBuild an AI technical assistant in minutes\n------------------------------------------\n\nTry it out\n\nFrequently asked questions\n--------------------------\n\nIf you have anything else you want to ask,[reach out to us](mailto:<EMAIL>)\n.\n\n*   *   ### Is it free?\n        \n        We have a free plan that gives you 500 message credits. It is also free for certain Open source projects. Contact us to see if your project is eligible.\n        \n    *   ### Do you train your AI model with my code?\n        \n        Currently, Mendable does not look at any of your repository's code. However, in the future we may add it. We will always give you the option to opt out of sharing your data.\n        \n*   *   ### How do I remove the Powered by Mendable?\n        \n        To remove the Powered by Mendable, you need to upgrade to an enterprise or custom plan. Contact us at [<EMAIL>](mailto:<EMAIL>)\n         and we can help you out.\n        \n    *   ### How do I get an anon key?\n        \n        To get your anon key you need to sign up at [mendable.ai](https://mendable.ai)\n         and create a project. Then you can find your anon key in the API Keys section of the dashboard. Anon keys are used for client-side while API keys are used for server-side.\n        \n*   *   ### Which model does Mendable use?\n        \n        Mendable offers gpt-3.5-turbo, gpt-4, claude-2 and more. If you'd like a custom model, contact us and we can help you out.\n        \n    *   ### Is GPT-4 pricing different?\n        \n        Yes, right now GPT-4 will cost 3 requests per message instead of 1 (gpt-3.5-turbo). That means that instead of 500 messages, you will get around 166 messages if you only use GPT-4.\n        \n*   *   ### Can you correct the AI response?\n        \n        Yes, Mendable offers a 'teach the model' functionality where you can correct the AI response and it will learn from it.\n        \n    *   ### How can I integrate Mendable with my application?\n        \n        Probably! Check out the Mendable documentation here [https://docs.mendable.ai](https://docs.mendable.ai)\n         to better understand how you can start integrating.\n        \n*   *   ### Is it 100% accurate?\n        \n        Like Humans, AI will never be 100% accurate. So we can't assure you that every solution will be correct.\n        \n    *   ### How do I cancel my subscription?\n        \n        Simply log into our platform, go to your account and click on \"Open customer portal\" button. There you will be able to cancel/modify it through Stripe.\n        \n*   *   ### How does Mendable work?\n        \n        Our application syncs with your documentation and support channels, then uses your docs and previously answered questions to suggest possible answers.\n        \n    *   ### Are you open-source?\n        \n        Currently not - although we have some open source components and integrations. If you have input here, please message us at.[<EMAIL>](mailto:<EMAIL>)\n        .\n        \n*   *   ### How does Mendable price custom plans?\n        \n        #### 1\\. Use case\n        \n        *   Mendable differentiates between internal and external use cases.\n        *   With Mendable, we give you the ability to use our chat bots for a variety of use cases, both for internal efficiency and external communication to your customers.\n        \n        #### 2\\. Total usage\n        \n        *   For specifically external use cases, you will only pay for the value you're receiving.\n        *   Mendable will look at the total number of messages sent during a month.\n        \n        #### 3\\. Custom work\n        \n        *   If there are any special feature requests (custom data connectors, etc.), we are happy to discuss these requirements!\n        \n\nWe use tracking cookies to understand how you use the product and help us improve it!  \nPlease accept cookies to help us improve.\n\nAccept CookiesDecline Cookies\n\n![Mendable logo](/Frame 566 (2).png)[Mendable](#_)\n\n[Instagram](https://instagram.com/sideguide.dev)\n[Twitter](https://twitter.com/mendableai)\n[GitHub](https://github.com/sideguide)\n[Discord](https://discord.com/invite/kJufGDb7AA)\n\n![SOC 2 Type II](/soc2type2badge.png)\n\nDocumentation\n\n*   [Getting Started](/signup)\n    \n\n*   [API Docs](https://docs.mendable.ai)\n    \n\n*   [Integrations](https://docs.mendable.ai/integrations/slack)\n    \n\n*   [Examples](https://docs.mendable.ai/examples)\n    \n\n*   [Tools & Actions](https://docs.mendable.ai/tools)\n    \n\nUse Cases\n\n*   [Sales Enablement](/usecases/sales-enablement)\n    \n\n*   [Knowledge Base](/usecases/documentation)\n    \n\n*   [CS Enablement](/usecases/cs-enablement)\n    \n\n*   [Product Copilot](/usecases/productcopilot)\n    \n\nResources\n\n*   [Pricing](/pricing)\n    \n\n*   [Changelog](https://docs.mendable.ai/changelog)\n    \n\n*   [Security](/security)\n    \n\n*   [AI Trust Center](https://mendable.wolfia.com/?ref=mendable-footer)\n    \n\nCompany\n\n*   [Blog](/blog)\n    \n\n*   [Contact](mailto:<EMAIL>)\n    \n\n© 2024 SideGuide - SideGuide Technologies Inc.\n\n[System Status](https://mendable.betteruptime.com)\n\n[Status](https://mendable.betteruptime.com)\n[Privacy Policy](/privacy-policy)\n[Privacy](/privacy-policy)\n[Terms](/terms-of-conditions)", "metadata": {"title": "Mendable", "description": "Mendable allows you to easily build AI chat applications. Ingest, customize, then deploy with one line of code anywhere you want. Brought to you by SideGuide", "robots": "follow, index", "ogTitle": "Mendable", "ogDescription": "Mendable allows you to easily build AI chat applications. Ingest, customize, then deploy with one line of code anywhere you want. Brought to you by SideGuide", "ogUrl": "https://mendable.ai/", "ogImage": "https://mendable.ai/mendable_new_og1.png", "ogLocaleAlternate": [], "ogSiteName": "Mendable", "sourceURL": "https://mendable.ai", "sitemap": {"changefreq": "hourly"}}}}